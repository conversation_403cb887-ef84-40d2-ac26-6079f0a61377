import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
	<PERSON><PERSON><PERSON>,
	Bar,
	XAxis,
	YAxis,
	CartesianGrid,
	Tooltip,
	Responsive<PERSON>ontainer,
	<PERSON><PERSON><PERSON>,
	<PERSON>,
	Cell,
	Legend,
} from "recharts";
import { useTranslations } from "next-intl";
import { api } from "@/lib/trpc/react";

// Mock data for charts
const adoptionData = [
	{ month: "Jan", count: 65 },
	{ month: "Feb", count: 59 },
	{ month: "Mar", count: 80 },
	{ month: "Apr", count: 81 },
	{ month: "May", count: 56 },
	{ month: "Jun", count: 55 },
	{ month: "Jul", count: 40 },
];

const catStatusData = [
	{ name: "Available", value: 325 },
	{ name: "Adopted", value: 1243 },
	{ name: "Pending", value: 87 },
];

const COLORS = ["#3b82f6", "#10b981", "#f59e0b"];

export function AdminStats() {
	const t = useTranslations("admin.stats");
	const {data:stats} = api.admin.getStats.useQuery();

	return (
		<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
			<Card>
				<CardHeader>
					<CardTitle>{t("adoptionStatistics")}</CardTitle>
				</CardHeader>
				<CardContent>
					<Tabs defaultValue="monthly">
						<TabsList className="mb-4">
							<TabsTrigger value="monthly">
								{t("monthly")}
							</TabsTrigger>
							<TabsTrigger value="weekly">
								{t("weekly")}
							</TabsTrigger>
						</TabsList>
						<TabsContent value="monthly">
							<div className="h-80">
								<ResponsiveContainer width="100%" height="100%">
									<BarChart
										data={adoptionData}
										margin={{
											top: 5,
											right: 30,
											left: 20,
											bottom: 5,
										}}
									>
										<CartesianGrid strokeDasharray="3 3" />
										<XAxis dataKey="month" />
										<YAxis />
										<Tooltip />
										<Bar dataKey="count" fill="#3b82f6" />
									</BarChart>
								</ResponsiveContainer>
							</div>
						</TabsContent>
						<TabsContent value="weekly">
							<div className="h-80 flex items-center justify-center text-muted-foreground">
								{t("weeklyDataNotAvailable")}
							</div>
						</TabsContent>
					</Tabs>
				</CardContent>
			</Card>

			<Card>
				<CardHeader>
					<CardTitle>{t("catStatusOverview")}</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="h-80">
						<ResponsiveContainer width="100%" height="100%">
							<PieChart>
								<Pie
									data={catStatusData}
									cx="50%"
									cy="50%"
									labelLine={false}
									outerRadius={80}
									fill="#8884d8"
									dataKey="value"
									label={({ name, percent }) =>
										`${name} ${(percent * 100).toFixed(0)}%`
									}
								>
									{catStatusData.map((_, index) => (
										<Cell
											key={`cell-${index}`}
											fill={COLORS[index % COLORS.length]}
										/>
									))}
								</Pie>
								<Tooltip />
								<Legend />
							</PieChart>
						</ResponsiveContainer>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
